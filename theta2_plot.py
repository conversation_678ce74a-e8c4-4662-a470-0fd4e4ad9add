import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 读取附件1.csv文件
df = pd.read_csv('附件1.csv')

# 获取列名
wavenumber_label = df.columns[0]  # 波数列名
reflectance_label = df.columns[1]  # 反射率列名

# 提取数据
wavenumber = df.iloc[:, 0]  # 波数
R = df.iloc[:, 1] / 100  # 反射率，转换为小数形式（0-1）

# 设置参数
theta1_deg = 10  # theta1 = 10度
theta1_rad = np.radians(theta1_deg)  # 转换为弧度

# 根据公式计算theta2
# theta2 = arctan((1-R)/(R+1) * tan(theta1))
# 注意：当R接近-1时，分母R+1接近0，需要处理
theta2_rad = np.arctan((1 - R) / (R + 1) * np.tan(theta1_rad))
theta2_deg = np.degrees(theta2_rad)  # 转换为度

# 创建图表1：theta2 vs R
plt.figure(figsize=(12, 8))
plt.plot(R * 100, theta2_deg, linewidth=1.5, color='blue', label=f'theta1 = {theta1_deg}度')

# 设置图表标题和标签（避免下标字符问题）
plt.title(f'theta2 关于反射率 R 的关系曲线 (theta1 = {theta1_deg}度)', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('反射率 R (%)', fontsize=14)
plt.ylabel('theta2 (度)', fontsize=14)

# 设置网格
plt.grid(True, alpha=0.3, linestyle='--')

# 设置坐标轴范围
plt.xlim(0, 100)
theta2_range = theta2_deg.max() - theta2_deg.min()
plt.ylim(theta2_deg.min() - theta2_range * 0.05, theta2_deg.max() + theta2_range * 0.05)

# 添加图例
plt.legend(fontsize=12)

# 美化图表
plt.tight_layout()

# 添加统计信息
info_text = f'数据统计:\ntheta2 范围: {theta2_deg.min():.2f}度 - {theta2_deg.max():.2f}度\n反射率范围: {(R*100).min():.1f}% - {(R*100).max():.1f}%'
plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
         fontsize=10)

# 保存图片
plt.savefig('theta2_vs_R.png', dpi=300, bbox_inches='tight')
plt.show()

# 创建图表2：theta2 vs 波数（显示theta2随波数的变化）
plt.figure(figsize=(12, 8))
plt.plot(wavenumber, theta2_deg, linewidth=1.5, color='red', label=f'θ₁ = {theta1_deg}°')

plt.title(f'θ₂ 关于波数的关系曲线 (θ₁ = {theta1_deg}°)', fontsize=16, fontweight='bold', pad=20)
plt.xlabel(wavenumber_label, fontsize=14)
plt.ylabel('θ₂ (度)', fontsize=14)

plt.grid(True, alpha=0.3, linestyle='--')
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(theta2_deg.min() - theta2_range * 0.05, theta2_deg.max() + theta2_range * 0.05)

plt.legend(fontsize=12)
plt.tight_layout()

# 添加统计信息
info_text2 = f'数据统计:\nθ₂ 范围: {theta2_deg.min():.2f}° - {theta2_deg.max():.2f}°\n波数范围: {wavenumber.min():.1f} - {wavenumber.max():.1f} cm⁻¹'
plt.text(0.02, 0.98, info_text2, transform=plt.gca().transAxes, 
         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
         fontsize=10)

plt.savefig('theta2_vs_wavenumber.png', dpi=300, bbox_inches='tight')
plt.show()

# 输出统计信息
print("计算完成！")
print(f"θ₁ = {theta1_deg}°")
print(f"反射率 R 范围: {(R*100).min():.2f}% - {(R*100).max():.2f}%")
print(f"θ₂ 范围: {theta2_deg.min():.2f}° - {theta2_deg.max():.2f}°")
print(f"波数范围: {wavenumber.min():.2f} - {wavenumber.max():.2f} cm⁻¹")

# 找出一些特殊点
max_theta2_idx = theta2_deg.idxmax()
min_theta2_idx = theta2_deg.idxmin()

print(f"\n特殊点分析:")
print(f"最大 θ₂ = {theta2_deg.max():.2f}°, 对应反射率 R = {(R*100).iloc[max_theta2_idx]:.2f}%, 波数 = {wavenumber.iloc[max_theta2_idx]:.2f} cm⁻¹")
print(f"最小 θ₂ = {theta2_deg.min():.2f}°, 对应反射率 R = {(R*100).iloc[min_theta2_idx]:.2f}%, 波数 = {wavenumber.iloc[min_theta2_idx]:.2f} cm⁻¹")

print(f"\n图表已保存为:")
print("- theta2_vs_R.png (θ₂ vs 反射率)")
print("- theta2_vs_wavenumber.png (θ₂ vs 波数)")
