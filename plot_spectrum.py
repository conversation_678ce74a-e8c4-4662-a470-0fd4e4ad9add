import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取CSV文件
df = pd.read_csv('附件1.csv')

# 提取数据
wavenumber = df['波数 (cm-1)']
reflectance = df['反射率 (%)']

# 创建图表
plt.figure(figsize=(12, 8))
plt.plot(wavenumber, reflectance, linewidth=1.5, color='blue')

# 设置图表标题和标签
plt.title('红外光谱 - 波数与反射率关系图', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('波数 (cm⁻¹)', fontsize=14)
plt.ylabel('反射率 (%)', fontsize=14)

# 设置网格
plt.grid(True, alpha=0.3, linestyle='--')

# 设置坐标轴范围
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(0, 100)

# 美化图表
plt.tight_layout()

# 添加一些统计信息
max_reflectance = reflectance.max()
min_reflectance = reflectance.min()
max_wavenumber = wavenumber[reflectance.idxmax()]
min_wavenumber = wavenumber[reflectance.idxmin()]

print(f"数据统计信息：")
print(f"波数范围: {wavenumber.min():.2f} - {wavenumber.max():.2f} cm⁻¹")
print(f"反射率范围: {min_reflectance:.2f}% - {max_reflectance:.2f}%")
print(f"最大反射率 {max_reflectance:.2f}% 对应波数: {max_wavenumber:.2f} cm⁻¹")
print(f"最小反射率 {min_reflectance:.2f}% 对应波数: {min_wavenumber:.2f} cm⁻¹")

# 保存图片
plt.savefig('spectrum_plot.png', dpi=300, bbox_inches='tight')
plt.show()

# 创建一个更详细的分析图，显示不同波数区间的特征
plt.figure(figsize=(15, 10))

# 主图
plt.subplot(2, 1, 1)
plt.plot(wavenumber, reflectance, linewidth=1.2, color='darkblue')
plt.title('完整光谱图', fontsize=14, fontweight='bold')
plt.xlabel('波数 (cm⁻¹)', fontsize=12)
plt.ylabel('反射率 (%)', fontsize=12)
plt.grid(True, alpha=0.3)
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(0, 100)

# 局部放大图 - 选择一个有趣的区间
mid_point = len(wavenumber) // 2
start_idx = max(0, mid_point - 500)
end_idx = min(len(wavenumber), mid_point + 500)

plt.subplot(2, 1, 2)
plt.plot(wavenumber[start_idx:end_idx], reflectance[start_idx:end_idx], 
         linewidth=1.5, color='red', marker='o', markersize=2)
plt.title(f'局部放大图 (波数范围: {wavenumber.iloc[start_idx]:.1f} - {wavenumber.iloc[end_idx-1]:.1f} cm⁻¹)', 
          fontsize=14, fontweight='bold')
plt.xlabel('波数 (cm⁻¹)', fontsize=12)
plt.ylabel('反射率 (%)', fontsize=12)
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('detailed_spectrum_plot.png', dpi=300, bbox_inches='tight')
plt.show()
