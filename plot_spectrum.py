import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 读取CSV文件
df = pd.read_csv('附件1.csv')

# 提取数据
wavenumber = df['波数 (cm-1)']
reflectance = df['反射率 (%)']

# 创建图表
plt.figure(figsize=(12, 8))
plt.plot(wavenumber, reflectance, linewidth=1.5, color='blue')

# 设置图表标题和标签
plt.title('Infrared Spectrum - Wavenumber vs Reflectance', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('Wavenumber (cm⁻¹)', fontsize=14)
plt.ylabel('Reflectance (%)', fontsize=14)

# 设置网格
plt.grid(True, alpha=0.3, linestyle='--')

# 设置坐标轴范围
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(0, 100)

# 美化图表
plt.tight_layout()

# 添加一些统计信息
max_reflectance = reflectance.max()
min_reflectance = reflectance.min()
max_wavenumber = wavenumber[reflectance.idxmax()]
min_wavenumber = wavenumber[reflectance.idxmin()]

print(f"Data Statistics:")
print(f"Wavenumber range: {wavenumber.min():.2f} - {wavenumber.max():.2f} cm⁻¹")
print(f"Reflectance range: {min_reflectance:.2f}% - {max_reflectance:.2f}%")
print(f"Max reflectance {max_reflectance:.2f}% at wavenumber: {max_wavenumber:.2f} cm⁻¹")
print(f"Min reflectance {min_reflectance:.2f}% at wavenumber: {min_wavenumber:.2f} cm⁻¹")

# 保存图片
plt.savefig('spectrum_plot.png', dpi=300, bbox_inches='tight')
plt.show()

# 创建一个更详细的分析图，显示不同波数区间的特征
plt.figure(figsize=(15, 10))

# 主图
plt.subplot(2, 1, 1)
plt.plot(wavenumber, reflectance, linewidth=1.2, color='darkblue')
plt.title('Complete Spectrum', fontsize=14, fontweight='bold')
plt.xlabel('Wavenumber (cm⁻¹)', fontsize=12)
plt.ylabel('Reflectance (%)', fontsize=12)
plt.grid(True, alpha=0.3)
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(0, 100)

# 局部放大图 - 选择一个有趣的区间
mid_point = len(wavenumber) // 2
start_idx = max(0, mid_point - 500)
end_idx = min(len(wavenumber), mid_point + 500)

plt.subplot(2, 1, 2)
plt.plot(wavenumber[start_idx:end_idx], reflectance[start_idx:end_idx],
         linewidth=1.5, color='red', marker='o', markersize=2)
plt.title(f'Zoomed View (Wavenumber range: {wavenumber.iloc[start_idx]:.1f} - {wavenumber.iloc[end_idx-1]:.1f} cm⁻¹)',
          fontsize=14, fontweight='bold')
plt.xlabel('Wavenumber (cm⁻¹)', fontsize=12)
plt.ylabel('Reflectance (%)', fontsize=12)
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('detailed_spectrum_plot.png', dpi=300, bbox_inches='tight')
plt.show()
