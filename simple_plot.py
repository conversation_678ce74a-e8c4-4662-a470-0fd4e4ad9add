import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取CSV文件
df = pd.read_csv('附件1.csv')

# 提取数据
wavenumber = df.iloc[:, 0]  # 第一列：波数
reflectance = df.iloc[:, 1]  # 第二列：反射率

# 创建图表
plt.figure(figsize=(12, 8))
plt.plot(wavenumber, reflectance, linewidth=1.5, color='blue')

# 设置图表标题和标签（使用英文避免字体问题）
plt.title('Infrared Spectrum - Wavenumber vs Reflectance', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('Wavenumber (cm-1)', fontsize=14)
plt.ylabel('Reflectance (%)', fontsize=14)

# 设置网格
plt.grid(True, alpha=0.3, linestyle='--')

# 设置坐标轴范围
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(0, 100)

# 美化图表
plt.tight_layout()

# 添加一些统计信息
max_reflectance = reflectance.max()
min_reflectance = reflectance.min()
max_wavenumber = wavenumber[reflectance.idxmax()]
min_wavenumber = wavenumber[reflectance.idxmin()]

print(f"Data Statistics:")
print(f"Wavenumber range: {wavenumber.min():.2f} - {wavenumber.max():.2f} cm-1")
print(f"Reflectance range: {min_reflectance:.2f}% - {max_reflectance:.2f}%")
print(f"Max reflectance {max_reflectance:.2f}% at wavenumber: {max_wavenumber:.2f} cm-1")
print(f"Min reflectance {min_reflectance:.2f}% at wavenumber: {min_wavenumber:.2f} cm-1")

# 保存图片
plt.savefig('spectrum_clean.png', dpi=300, bbox_inches='tight')
plt.show()

# 创建第二个图表 - 更详细的分析
plt.figure(figsize=(15, 10))

# 主图
plt.subplot(2, 1, 1)
plt.plot(wavenumber, reflectance, linewidth=1.2, color='darkblue')
plt.title('Complete Spectrum', fontsize=14, fontweight='bold')
plt.xlabel('Wavenumber (cm-1)', fontsize=12)
plt.ylabel('Reflectance (%)', fontsize=12)
plt.grid(True, alpha=0.3)
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(0, 100)

# 局部放大图 - 选择中间区域
mid_point = len(wavenumber) // 2
start_idx = max(0, mid_point - 500)
end_idx = min(len(wavenumber), mid_point + 500)

plt.subplot(2, 1, 2)
plt.plot(wavenumber[start_idx:end_idx], reflectance[start_idx:end_idx], 
         linewidth=1.5, color='red', marker='o', markersize=1.5)
plt.title(f'Zoomed View (Range: {wavenumber.iloc[start_idx]:.1f} - {wavenumber.iloc[end_idx-1]:.1f} cm-1)', 
          fontsize=14, fontweight='bold')
plt.xlabel('Wavenumber (cm-1)', fontsize=12)
plt.ylabel('Reflectance (%)', fontsize=12)
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('detailed_spectrum_clean.png', dpi=300, bbox_inches='tight')
plt.show()

print("\nGraphs saved as:")
print("- spectrum_clean.png")
print("- detailed_spectrum_clean.png")
