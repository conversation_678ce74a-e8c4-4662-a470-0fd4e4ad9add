import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 读取CSV文件
df = pd.read_csv('附件2.csv')

# 获取列名
x_label = df.columns[0]  # 第一列的列名
y_label = df.columns[1]  # 第二列的列名

# 提取数据
wavenumber = df.iloc[:, 0]  # 第一列：波数
reflectance = df.iloc[:, 1]  # 第二列：反射率

# 创建图表
plt.figure(figsize=(12, 8))
plt.plot(wavenumber, reflectance, linewidth=1.5, color='blue')

# 设置图表标题和标签（使用CSV文件中的中文列名）
plt.title(f'{y_label} vs {x_label}', fontsize=16, fontweight='bold', pad=20)
plt.xlabel(x_label, fontsize=14)
plt.ylabel(y_label, fontsize=14)

# 设置网格
plt.grid(True, alpha=0.3, linestyle='--')

# 设置坐标轴范围 - 动态调整纵轴以显示所有数据
plt.xlim(wavenumber.min(), wavenumber.max())
# 纵轴范围：从0到最大值的110%，确保所有数据都能显示
y_max = reflectance.max() * 1.1  # 增加10%的空间使图表更美观
plt.ylim(0, y_max)

# 美化图表
plt.tight_layout()

# 添加数据范围信息到图表上
info_text = f'数据范围:\n{x_label}: {wavenumber.min():.1f} - {wavenumber.max():.1f}\n{y_label}: {reflectance.min():.1f} - {reflectance.max():.1f}'
plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
         fontsize=10)

# 添加一些统计信息
max_reflectance = reflectance.max()
min_reflectance = reflectance.min()
max_wavenumber = wavenumber[reflectance.idxmax()]
min_wavenumber = wavenumber[reflectance.idxmin()]

print(f"数据统计信息:")
print(f"{x_label}范围: {wavenumber.min():.2f} - {wavenumber.max():.2f}")
print(f"{y_label}范围: {min_reflectance:.2f} - {max_reflectance:.2f}")
print(f"最大{y_label} {max_reflectance:.2f} 对应{x_label}: {max_wavenumber:.2f}")
print(f"最小{y_label} {min_reflectance:.2f} 对应{x_label}: {min_wavenumber:.2f}")

# 保存图片
plt.savefig('spectrum_clean.png', dpi=300, bbox_inches='tight')
plt.show()

# 创建第二个图表 - 更详细的分析
plt.figure(figsize=(15, 10))

# 主图
plt.subplot(2, 1, 1)
plt.plot(wavenumber, reflectance, linewidth=1.2, color='darkblue')
plt.title('完整光谱图', fontsize=14, fontweight='bold')
plt.xlabel(x_label, fontsize=12)
plt.ylabel(y_label, fontsize=12)
plt.grid(True, alpha=0.3)
plt.xlim(wavenumber.min(), wavenumber.max())
plt.ylim(0, y_max)  # 使用相同的动态纵轴范围

# 局部放大图 - 选择中间区域
mid_point = len(wavenumber) // 2
start_idx = max(0, mid_point - 500)
end_idx = min(len(wavenumber), mid_point + 500)

plt.subplot(2, 1, 2)
plt.plot(wavenumber[start_idx:end_idx], reflectance[start_idx:end_idx],
         linewidth=1.5, color='red', marker='o', markersize=1.5)
plt.title(f'局部放大图 (范围: {wavenumber.iloc[start_idx]:.1f} - {wavenumber.iloc[end_idx-1]:.1f})',
          fontsize=14, fontweight='bold')
plt.xlabel(x_label, fontsize=12)
plt.ylabel(y_label, fontsize=12)
plt.grid(True, alpha=0.3)

# 为局部图也设置合适的纵轴范围
local_y_max = reflectance[start_idx:end_idx].max() * 1.1
local_y_min = max(0, reflectance[start_idx:end_idx].min() - reflectance[start_idx:end_idx].max() * 0.05)
plt.ylim(local_y_min, local_y_max)

plt.tight_layout()
plt.savefig('detailed_spectrum_clean.png', dpi=300, bbox_inches='tight')
plt.show()

print("\n图表已保存为:")
print("- spectrum_clean.png (主光谱图)")
print("- detailed_spectrum_clean.png (详细分析图)")
